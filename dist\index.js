#!/usr/bin/env node
import { Command } from 'commander';
import { fileURLToPath } from 'url';
import { setupConfig } from './utils/config.js';
import { logger } from './utils/logger.js';
import fs from 'fs/promises';
import path from 'path';
/**
 * Main entry point for the autonomous agent CLI
 */
async function main() {
    try {
        // Get package.json path and version
        const __filename = fileURLToPath(import.meta.url);
        const __dirname = path.dirname(__filename);
        const packageJsonPath = path.join(__dirname, '..', 'package.json');
        const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
        const { version } = packageJson;
        const program = new Command();
        program
            .name('agent')
            .description('Autonomous AI Agent CLI Tool')
            .version(version);
        // Main command
        program
            .argument('[input]', 'Optional command or question to process')
            .option('-v, --verbose', 'Enable verbose logging')
            .option('--model <model>', 'Specify the AI model to use')
            .option('--provider <provider>', 'Specify the AI provider (openai, deepseek, ollama)')
            .option('--config <path>', 'Path to custom configuration file')
            .action(async (input, options) => {
            try {
                // Initialize configuration
                const config = await setupConfig(options.config);
                // Process input or start interactive mode
                if (input) {
                    logger.info(`Processing input: ${input}`);
                    console.log(`🤖 Processing: ${input}`);
                    console.log(`✅ This is a minimal working version. Full AI capabilities will be added later.`);
                }
                else {
                    // Start interactive mode
                    console.log(`🤖 AI Agent CLI Tool v${version}`);
                    console.log(`✅ Minimal version running successfully!`);
                    console.log(`📝 Configuration loaded successfully`);
                    console.log(`🔧 Use 'npm run dev' to start development mode`);
                    console.log(`💡 Full interactive mode will be available in the complete version`);
                }
            }
            catch (error) {
                logger.error('Error in agent execution:', error);
                process.exit(1);
            }
        });
        // Configuration command
        program
            .command('config')
            .description('Configure the agent settings')
            .action(async () => {
            try {
                await setupConfig(undefined, true);
                logger.info('Configuration updated successfully');
            }
            catch (error) {
                logger.error('Error updating configuration:', error);
                process.exit(1);
            }
        });
        // Parse command line arguments
        await program.parseAsync(process.argv);
    }
    catch (error) {
        logger.error('Fatal error:', error);
        process.exit(1);
    }
}
main().catch((error) => {
    logger.error('Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map