import { ToolRegistry } from './ToolRegistry.js';
import { logger } from '../utils/logger.js';
// Import tool categories
import { registerFileTools } from './file/registerFileTools.js';
import { registerShellTools } from './shell/registerShellTools.js';
/**
 * Register all default tools
 * @returns Initialized ToolRegistry
 */
export function registerDefaultTools() {
    logger.info('Registering default tools');
    const toolRegistry = new ToolRegistry();
    try {
        // Register file tools
        registerFileTools(toolRegistry);
        // Register shell tools
        registerShellTools(toolRegistry);
        logger.info(`Registered ${toolRegistry.getToolCount()} tools`);
    }
    catch (error) {
        logger.error('Error registering tools:', error);
    }
    return toolRegistry;
}
//# sourceMappingURL=registerDefaultTools.js.map