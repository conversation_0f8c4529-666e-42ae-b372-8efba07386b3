import { Config } from '../utils/config.js';
import { logger } from '../utils/logger.js';
import { OpenAIProvider } from './providers/OpenAIProvider.js';
import { DeepseekProvider } from './providers/DeepseekProvider.js';
import { OllamaProvider } from './providers/OllamaProvider.js';
import { BaseProvider } from './providers/BaseProvider.js';
import { ProviderType } from './types.js';

/**
 * Manages AI providers
 */
export class ProviderManager {
  private providers: Map<ProviderType, BaseProvider> = new Map();
  private currentProvider: ProviderType;
  private config: Config;
  
  constructor(config: Config) {
    this.config = config;
    this.currentProvider = this.getDefaultProviderType();
    this.initializeProviders();
  }
  
  /**
   * Initialize providers based on configuration
   */
  private initializeProviders(): void {
    try {
      // Initialize OpenAI provider if configured
      if (this.config.ai.providers.openai?.apiKey) {
        this.providers.set(ProviderType.OPENAI, new OpenAIProvider({
          apiKey: this.config.ai.providers.openai.apiKey,
          baseURL: this.config.ai.providers.openai.baseUrl,
          defaultModel: this.config.ai.providers.openai.defaultModel,
          config: this.config
        }));
        logger.info('OpenAI provider initialized');
      }
      
      // TODO: Initialize Deepseek provider if configured
      
      // TODO: Initialize Ollama provider if configured
      
      // Check if any providers were initialized
      if (this.providers.size === 0) {
        logger.warn('No AI providers initialized. Please configure at least one provider.');
      }
    } catch (error) {
      logger.error('Error initializing providers:', error);
      throw error;
    }
  }
  
  /**
   * Get the current provider
   */
  public getCurrentProvider(): BaseProvider {
    const provider = this.providers.get(this.currentProvider);
    
    if (!provider) {
      throw new Error(`Provider ${this.currentProvider} not initialized`);
    }
    
    return provider;
  }
  
  /**
   * Set the current provider
   */
  public setCurrentProvider(providerType: ProviderType): void {
    if (!this.providers.has(providerType)) {
      throw new Error(`Provider ${providerType} not initialized`);
    }
    
    this.currentProvider = providerType;
    logger.info(`Current provider set to ${providerType}`);
  }
  
  /**
   * Get the default provider type from config
   */
  private getDefaultProviderType(): ProviderType {
    const configuredProvider = this.config.ai.defaultProvider?.toLowerCase();
    
    if (!configuredProvider) {
      return ProviderType.OPENAI; // Default to OpenAI if not specified
    }
    
    // Convert string to enum
    if (configuredProvider === 'openai') {
      return ProviderType.OPENAI;
    } else if (configuredProvider === 'deepseek') {
      return ProviderType.DEEPSEEK;
    } else if (configuredProvider === 'ollama') {
      return ProviderType.OLLAMA;
    }
    
    // Default to OpenAI if the provider is not recognized
    logger.warn(`Unknown provider type: ${configuredProvider}, defaulting to OpenAI`);
    return ProviderType.OPENAI;
  }
  
  /**
   * Get all initialized providers
   */
  public getAvailableProviders(): ProviderType[] {
    return Array.from(this.providers.keys());
  }
  
  /**
   * Get the current provider name
   */
  public getCurrentProviderName(): string {
    return this.currentProvider;
  }
  
  /**
   * Get the current model name
   */
  public getCurrentModelName(): string {
    return this.getCurrentProvider().getModelName();
  }
  
  /**
   * Set the API key for a provider
   */
  public setApiKey(providerName: string, apiKey: string): void {
    const provider = this.providers.get(providerName);
    
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }
    
    provider.setApiKey(apiKey);
    logger.info(`Updated API key for provider: ${providerName}`);
  }
  
  /**
   * Set the base URL for a provider
   */
  public setBaseUrl(providerName: string, baseUrl: string): void {
    const provider = this.providers.get(providerName);
    
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }
    
    provider.setBaseUrl(baseUrl);
    logger.info(`Updated base URL for provider: ${providerName}: ${baseUrl}`);
  }
  
  /**
   * Update configuration for all providers
   */
  public updateConfig(config: Config): void {
    this.config = config;

    // Update all providers with new config
    for (const [name, provider] of this.providers.entries()) {
      provider.updateConfig(config);
      logger.debug(`Updated config for provider: ${name}`);
    }
  }

  /**
   * Register a provider
   */
  public registerProvider(type: ProviderType, provider: BaseProvider): void {
    this.providers.set(type, provider);
    logger.info(`Registered provider: ${type}`);
  }

  /**
   * Check if a provider is available
   */
  public hasProvider(type: ProviderType): boolean {
    return this.providers.has(type);
  }

  /**
   * Get a specific provider
   */
  public getProvider(type: ProviderType): BaseProvider | undefined {
    return this.providers.get(type);
  }

  /**
   * Remove a provider
   */
  public removeProvider(type: ProviderType): boolean {
    const result = this.providers.delete(type);
    if (result) {
      logger.info(`Removed provider: ${type}`);

      // If we removed the current provider, switch to another one
      if (this.currentProvider === type) {
        const availableProviders = this.getAvailableProviders();
        if (availableProviders.length > 0) {
          this.currentProvider = availableProviders[0];
          logger.info(`Switched to provider: ${this.currentProvider}`);
        }
      }
    }
    return result;
  }

  /**
   * Get provider count
   */
  public getProviderCount(): number {
    return this.providers.size;
  }
}