import { EventEmitter } from 'events';
import { ToolCall } from './types.js';
import { logger } from '../utils/logger.js';

/**
 * Handles streaming responses from AI providers
 */
export class StreamingResponseHandler extends EventEmitter {
  private toolCalls: Map<string, ToolCall> = new Map();
  private isComplete: boolean = false;
  private hasError: boolean = false;
  private completePromise: Promise<void>;
  private completeResolve!: () => void;
  private completeReject!: (error: Error) => void;

  constructor() {
    super();
    
    // Create a promise that will resolve when the response is complete
    this.completePromise = new Promise<void>((resolve, reject) => {
      this.completeResolve = resolve;
      this.completeReject = reject;
    });
  }

  /**
   * Handle content from the AI response
   */
  public handleContent(content: string): void {
    this.emit('content', content);
  }

  /**
   * Handle a tool call from the AI response
   */
  public handleToolCall(toolCall: any): void {
    // Skip if no id
    if (!toolCall.id) {
      return;
    }

    // Get or create the tool call
    let existingToolCall = this.toolCalls.get(toolCall.id);
    
    if (!existingToolCall) {
      existingToolCall = {
        id: toolCall.id,
        name: '',
        arguments: ''
      };
      this.toolCalls.set(toolCall.id, existingToolCall);
    }
    
    // Update the tool call with new information
    if (toolCall.function?.name) {
      existingToolCall.name = toolCall.function.name;
    }
    
    if (toolCall.function?.arguments) {
      existingToolCall.arguments += toolCall.function.arguments;
    }
    
    // Check if the tool call is complete
    if (existingToolCall.name && existingToolCall.arguments) {
      try {
        // Try to parse the arguments as JSON
        JSON.parse(existingToolCall.arguments);
        
        // If parsing succeeds, emit the tool call
        this.emit('toolCall', existingToolCall);
      } catch (error) {
        // If parsing fails, the arguments are incomplete
        logger.debug(`Tool call arguments incomplete: ${existingToolCall.arguments}`);
      }
    }
  }

  /**
   * Handle a tool call result
   */
  public handleToolCallResult(toolCallId: string, result: any): void {
    this.emit('toolCallResult', { id: toolCallId, result });
  }

  /**
   * Handle an error from the AI response
   */
  public error(error: Error): void {
    this.hasError = true;
    this.emit('error', error);
    this.completeReject(error);
  }

  /**
   * Signal that the response is complete
   */
  public complete(): void {
    this.isComplete = true;
    this.emit('complete');
    this.completeResolve();
  }

  /**
   * Wait for the response to complete
   */
  public async waitForCompletion(): Promise<void> {
    return this.completePromise;
  }

  /**
   * Check if the response is complete
   */
  public isCompleted(): boolean {
    return this.isComplete || this.hasError;
  }
} 