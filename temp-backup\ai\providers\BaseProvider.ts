import { StreamingResponseHandler } from '../StreamingResponseHandler.js';
import { Config } from '../../utils/config.js';
import { Tool } from '../../tools/ToolRegistry.js';
import { ProviderOptions, RequestOptions } from '../types.js';
import { logger } from '../../utils/logger.js';

export interface RequestOptions {
  input: string;
  context: Record<string, any>;
  tools: Tool[];
  responseHandler: StreamingResponseHandler;
  config: Config;
}

/**
 * Abstract base class for all AI providers
 */
export abstract class BaseProvider {
  protected config: Config;
  protected model: string;
  protected apiKey: string;
  protected baseUrl: string;
  protected baseURL?: string;
  protected organization?: string;

  constructor(options: ProviderOptions) {
    this.config = options.config;
    this.model = this.config.ai.model;
    this.apiKey = options.apiKey || this.getApiKeyFromConfig(this.config);
    this.baseUrl = this.getBaseUrlFromConfig(this.config);
    this.baseURL = options.baseURL;
    this.organization = options.organization;
    
    if (!this.apiKey) {
      logger.warn('No API key provided for provider');
    }
  }

  /**
   * Send a request to the AI provider
   * @param options Request options
   */
  public abstract sendRequest(options: RequestOptions): Promise<void>;

  /**
   * Get the model name
   */
  public getModelName(): string {
    return this.model;
  }

  /**
   * Set the model name
   */
  public setModel(model: string): void {
    this.model = model;
  }

  /**
   * Set the API key
   */
  public setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * Set the base URL
   */
  public setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Config): void {
    this.config = config;
    this.model = config.ai.model;
    this.apiKey = this.getApiKeyFromConfig(config);
    this.baseUrl = this.getBaseUrlFromConfig(config);
  }

  /**
   * Extract API key from config
   */
  protected abstract getApiKeyFromConfig(config: Config): string;

  /**
   * Extract base URL from config
   */
  protected abstract getBaseUrlFromConfig(config: Config): string;
} 