import { ProviderManager } from './ProviderManager.js';
import { OpenAIProvider } from './providers/OpenAIProvider.js';
import { DeepseekProvider } from './providers/DeepseekProvider.js';
import { OllamaProvider } from './providers/OllamaProvider.js';
import { ProviderType, ProviderOptions } from './types.js';
import { Config } from '../utils/config.js';
import { logger } from '../utils/logger.js';

/**
 * Setup and initialize AI providers based on configuration
 */
export async function setupProviders(
  config: Config,
  preferredProvider?: string,
  preferredModel?: string
): Promise<ProviderManager> {
  logger.info('Setting up AI providers');

  const providerManager = new ProviderManager(config);

  try {
    // Initialize OpenAI provider if configured
    if (config.ai.providers.openai?.apiKey || process.env.OPENAI_API_KEY) {
      const openaiOptions: ProviderOptions = {
        apiKey: config.ai.providers.openai?.apiKey || process.env.OPENAI_API_KEY,
        baseURL: config.ai.providers.openai?.baseUrl || 'https://api.openai.com/v1',
        defaultModel: config.ai.providers.openai?.defaultModel || 'gpt-4o',
        config,
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        maxConcurrentRequests: 5
      };

      const openaiProvider = new OpenAIProvider(openaiOptions);
      await testProvider(openaiProvider, 'OpenAI');
      providerManager.registerProvider(ProviderType.OPENAI, openaiProvider);
      logger.info('OpenAI provider initialized successfully');
    } else {
      logger.warn('OpenAI API key not found, skipping OpenAI provider initialization');
    }

    // Initialize Deepseek provider if configured
    if (config.ai.providers.deepseek?.apiKey || process.env.DEEPSEEK_API_KEY) {
      const deepseekOptions: ProviderOptions = {
        apiKey: config.ai.providers.deepseek?.apiKey || process.env.DEEPSEEK_API_KEY,
        baseURL: config.ai.providers.deepseek?.baseUrl || 'https://api.deepseek.com/v1',
        defaultModel: config.ai.providers.deepseek?.defaultModel || 'deepseek-coder',
        config,
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        maxConcurrentRequests: 3
      };

      const deepseekProvider = new DeepseekProvider(deepseekOptions);
      await testProvider(deepseekProvider, 'Deepseek');
      providerManager.registerProvider(ProviderType.DEEPSEEK, deepseekProvider);
      logger.info('Deepseek provider initialized successfully');
    } else {
      logger.warn('Deepseek API key not found, skipping Deepseek provider initialization');
    }

    // Initialize Ollama provider if configured
    if (config.ai.providers.ollama?.baseUrl || process.env.OLLAMA_BASE_URL) {
      const ollamaOptions: ProviderOptions = {
        baseURL: config.ai.providers.ollama?.baseUrl || process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
        defaultModel: config.ai.providers.ollama?.defaultModel || 'llama3',
        config,
        timeout: 60000,
        retryAttempts: 2,
        retryDelay: 2000,
        maxConcurrentRequests: 2
      };

      const ollamaProvider = new OllamaProvider(ollamaOptions);
      await testProvider(ollamaProvider, 'Ollama');
      providerManager.registerProvider(ProviderType.OLLAMA, ollamaProvider);
      logger.info('Ollama provider initialized successfully');
    } else {
      logger.info('Ollama base URL not configured, skipping Ollama provider initialization');
    }

    // Set preferred provider if specified
    if (preferredProvider) {
      const providerType = getProviderType(preferredProvider);
      if (providerType && providerManager.hasProvider(providerType)) {
        providerManager.setCurrentProvider(providerType);
        logger.info(`Set preferred provider to: ${preferredProvider}`);
      } else {
        logger.warn(`Preferred provider ${preferredProvider} not available, using default`);
      }
    }

    // Set preferred model if specified
    if (preferredModel) {
      const currentProvider = providerManager.getCurrentProvider();
      if (currentProvider) {
        currentProvider.setModel(preferredModel);
        logger.info(`Set preferred model to: ${preferredModel}`);
      }
    }

    // Verify at least one provider is available
    const availableProviders = providerManager.getAvailableProviders();
    if (availableProviders.length === 0) {
      throw new Error('No AI providers are available. Please configure at least one provider.');
    }

    logger.info(`Successfully initialized ${availableProviders.length} AI provider(s): ${availableProviders.join(', ')}`);
    return providerManager;

  } catch (error) {
    logger.error('Error setting up AI providers:', error);
    throw error;
  }
}

/**
 * Test a provider to ensure it's working correctly
 */
async function testProvider(provider: any, name: string): Promise<void> {
  try {
    logger.debug(`Testing ${name} provider connectivity`);

    // For now, we'll just check if the provider is properly initialized
    // In a production environment, you might want to make a simple API call
    if (!provider) {
      throw new Error(`${name} provider is not properly initialized`);
    }

    // Test basic functionality
    const modelName = provider.getModelName();
    if (!modelName) {
      throw new Error(`${name} provider does not have a valid model configured`);
    }

    logger.debug(`${name} provider test passed with model: ${modelName}`);

  } catch (error) {
    logger.error(`${name} provider test failed:`, error);
    throw new Error(`${name} provider is not working correctly: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Convert string provider name to ProviderType enum
 */
function getProviderType(providerName: string): ProviderType | null {
  const normalized = providerName.toLowerCase();

  switch (normalized) {
    case 'openai':
      return ProviderType.OPENAI;
    case 'deepseek':
      return ProviderType.DEEPSEEK;
    case 'ollama':
      return ProviderType.OLLAMA;
    default:
      return null;
  }
}

/**
 * Get default provider based on configuration and availability
 */
export function getDefaultProvider(config: Config): ProviderType {
  const configuredDefault = config.ai.defaultProvider?.toLowerCase();

  // Check if configured default is available
  if (configuredDefault) {
    const providerType = getProviderType(configuredDefault);
    if (providerType) {
      return providerType;
    }
  }

  // Fallback order: OpenAI -> Deepseek -> Ollama
  if (config.ai.providers.openai?.apiKey || process.env.OPENAI_API_KEY) {
    return ProviderType.OPENAI;
  }

  if (config.ai.providers.deepseek?.apiKey || process.env.DEEPSEEK_API_KEY) {
    return ProviderType.DEEPSEEK;
  }

  return ProviderType.OLLAMA;
}

/**
 * Validate provider configuration
 */
export function validateProviderConfig(config: Config): string[] {
  const issues: string[] = [];

  // Check OpenAI configuration
  if (config.ai.providers.openai) {
    if (!config.ai.providers.openai.apiKey && !process.env.OPENAI_API_KEY) {
      issues.push('OpenAI API key is missing');
    }
    if (!config.ai.providers.openai.defaultModel) {
      issues.push('OpenAI default model is not specified');
    }
  }

  // Check Deepseek configuration
  if (config.ai.providers.deepseek) {
    if (!config.ai.providers.deepseek.apiKey && !process.env.DEEPSEEK_API_KEY) {
      issues.push('Deepseek API key is missing');
    }
    if (!config.ai.providers.deepseek.defaultModel) {
      issues.push('Deepseek default model is not specified');
    }
  }

  // Check Ollama configuration
  if (config.ai.providers.ollama) {
    if (!config.ai.providers.ollama.baseUrl && !process.env.OLLAMA_BASE_URL) {
      issues.push('Ollama base URL is missing');
    }
    if (!config.ai.providers.ollama.defaultModel) {
      issues.push('Ollama default model is not specified');
    }
  }

  return issues;
}