import { StreamingResponseHandler } from './StreamingResponseHandler.js';
import { Config } from '../utils/config.js';
import { Tool } from '../tools/ToolRegistry.js';

/**
 * Provider type
 */
export enum ProviderType {
  OPENAI = 'openai',
  DEEPSEEK = 'deepseek',
  OLLAMA = 'ollama'
}

/**
 * Provider options for initializing AI providers
 */
export interface ProviderOptions {
  apiKey?: string;
  defaultModel?: string;
  baseURL?: string;
  organization?: string;
  config: Config;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  maxConcurrentRequests?: number;
}

/**
 * Request options for sending requests to AI providers
 */
export interface RequestOptions {
  input: string;
  context: Record<string, any>;
  tools: Tool[];
  responseHandler: StreamingResponseHandler;
  config: Config;
}

/**
 * Tool call from AI response
 */
export interface ToolCall {
  id: string;
  name: string;
  arguments: string;
}

/**
 * Tool call result
 */
export interface ToolCallResult {
  success: boolean;
  output: any;
  error?: Error;
}

/**
 * AI message interface
 */
export interface AIMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  name?: string;
}

/**
 * AI response chunk interface for streaming
 */
export interface AIResponseChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      role?: string;
      content?: string;
      tool_calls?: Array<{
        index?: number;
        id?: string;
        type?: string;
        function?: {
          name?: string;
          arguments?: string;
        };
      }>;
    };
    finish_reason?: string;
  }>;
}

/**
 * Provider capabilities interface
 */
export interface ProviderCapabilities {
  supportsStreaming: boolean;
  supportsToolCalls: boolean;
  supportsVision: boolean;
  supportsEmbeddings: boolean;
  maxTokens: number;
  supportedModels: string[];
}

/**
 * Embedding request interface
 */
export interface EmbeddingRequest {
  input: string | string[];
  model?: string;
  dimensions?: number;
}

/**
 * Embedding response interface
 */
export interface EmbeddingResponse {
  object: string;
  data: Array<{
    object: string;
    index: number;
    embedding: number[];
  }>;
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

/**
 * Chat completion request interface
 */
export interface ChatCompletionRequest {
  model: string;
  messages: AIMessage[];
  tools?: Array<{
    type: 'function';
    function: {
      name: string;
      description: string;
      parameters: Record<string, any>;
    };
  }>;
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
}

/**
 * Provider status interface
 */
export interface ProviderStatus {
  isAvailable: boolean;
  lastChecked: number;
  error?: string;
  latency?: number;
  model?: string;
}

/**
 * Provider metrics interface
 */
export interface ProviderMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  totalTokensUsed: number;
  lastRequestTime: number;
}

/**
 * Model information interface
 */
export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  supportsToolCalls: boolean;
  supportsVision: boolean;
  costPer1kTokens?: number;
}

/**
 * Error types for AI providers
 */
export enum AIErrorType {
  AUTHENTICATION = 'authentication',
  RATE_LIMIT = 'rate_limit',
  NETWORK = 'network',
  INVALID_REQUEST = 'invalid_request',
  MODEL_NOT_FOUND = 'model_not_found',
  CONTEXT_LENGTH_EXCEEDED = 'context_length_exceeded',
  CONTENT_FILTER = 'content_filter',
  UNKNOWN = 'unknown'
}

/**
 * AI error interface
 */
export interface AIError extends Error {
  type: AIErrorType;
  code?: string;
  statusCode?: number;
  retryable: boolean;
  details?: Record<string, any>;
}

/**
 * Token usage interface
 */
export interface TokenUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

/**
 * Response metadata interface
 */
export interface ResponseMetadata {
  model: string;
  usage?: TokenUsage;
  finish_reason?: string;
  latency: number;
  timestamp: number;
}

/**
 * Conversation context interface
 */
export interface ConversationContext {
  messages: AIMessage[];
  systemPrompt?: string;
  maxTokens?: number;
  temperature?: number;
  tools?: Tool[];
  metadata?: Record<string, any>;
}

/**
 * Provider health check result
 */
export interface HealthCheckResult {
  provider: ProviderType;
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency?: number;
  error?: string;
  timestamp: number;
}