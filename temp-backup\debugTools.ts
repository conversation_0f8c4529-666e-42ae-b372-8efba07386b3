import { z } from 'zod';
import { ToolRegistry } from './ToolRegistry.js';
import { AgentEngine } from '../agent/AgentEngine.js';
import { DebugManager, DebugEventType } from '../debug/DebugManager.js';
import { logger } from '../utils/logger.js';

// Reference to agent engine for accessing debug manager
let agentEngine: AgentEngine | null = null;

/**
 * Set the agent engine reference for debug tools
 */
export function setAgentEngineForDebugTools(engine: AgentEngine): void {
  agentEngine = engine;
}

/**
 * Get debug manager safely
 */
function getDebugManager(): DebugManager {
  if (!agentEngine) {
    throw new Error('Agent engine not set for debug tools');
  }
  
  return agentEngine.getDebugManager();
}

/**
 * Register all debugging tools
 */
export function registerDebugTools(registry: ToolRegistry): void {
  logger.info('Registering debug tools');
  
  // Start Debug Session Tool
  registry.registerTool({
    name: 'debug_start_session',
    description: 'Start a new debugging session',
    parameters: z.object({}),
    execute: async () => {
      const debugManager = getDebugManager();
      await debugManager.startDebuggingSession();
      return { success: true, message: 'Debugging session started' };
    }
  });
  
  // End Debug Session Tool
  registry.registerTool({
    name: 'debug_end_session',
    description: 'End the current debugging session',
    parameters: z.object({}),
    execute: async () => {
      const debugManager = getDebugManager();
      debugManager.endDebuggingSession();
      return { success: true, message: 'Debugging session ended' };
    }
  });
  
  // Run Command with Debug Tool
  registry.registerTool({
    name: 'debug_run_command',
    description: 'Run a command and capture its output for debugging',
    parameters: z.object({
      command: z.string().describe('The command to run')
    }),
    execute: async (params: { command: string }) => {
      const debugManager = getDebugManager();
      const result = await debugManager.runCommand(params.command);
      return result;
    }
  });
  
  // Get Detected Errors Tool
  registry.registerTool({
    name: 'debug_get_errors',
    description: 'Get all detected errors from the current debugging session',
    parameters: z.object({}),
    execute: async () => {
      const debugManager = getDebugManager();
      const errors = debugManager.getDetectedErrors();
      return { errors };
    }
  });
  
  // Generate Fix Suggestions Tool
  registry.registerTool({
    name: 'debug_generate_fixes',
    description: 'Generate fix suggestions for detected errors',
    parameters: z.object({}),
    execute: async () => {
      const debugManager = getDebugManager();
      const suggestions = await debugManager.generateFixSuggestions();
      return { suggestions };
    }
  });
  
  // Apply Fix Tool
  registry.registerTool({
    name: 'debug_apply_fix',
    description: 'Apply a fix to a file',
    parameters: z.object({
      file_path: z.string().describe('Path to the file to fix'),
      line: z.number().describe('Line number where the fix should be applied'),
      original_code: z.string().describe('Original code to replace'),
      fixed_code: z.string().describe('Fixed code to insert')
    }),
    execute: async (params: { file_path: string; line: number; original_code: string; fixed_code: string }) => {
      const debugManager = getDebugManager();
      const success = await debugManager.applyFix(
        params.file_path,
        params.line,
        params.original_code,
        params.fixed_code
      );
      
      return { success, message: success ? 'Fix applied successfully' : 'Failed to apply fix' };
    }
  });
  
  // Revert Fixes Tool
  registry.registerTool({
    name: 'debug_revert_fixes',
    description: 'Revert all applied fixes in the current debugging session',
    parameters: z.object({}),
    execute: async () => {
      const debugManager = getDebugManager();
      await debugManager.revertFixes();
      return { success: true, message: 'All fixes reverted' };
    }
  });
  
  // Run Tests Tool
  registry.registerTool({
    name: 'debug_run_tests',
    description: 'Run tests to verify fixes',
    parameters: z.object({
      test_command: z.string().describe('Test command to run')
    }),
    execute: async (params: { test_command: string }) => {
      const debugManager = getDebugManager();
      const success = await debugManager.runTests(params.test_command);
      return { success, message: success ? 'Tests passed' : 'Tests failed' };
    }
  });
  
  logger.info('Debug tools registered successfully');
} 