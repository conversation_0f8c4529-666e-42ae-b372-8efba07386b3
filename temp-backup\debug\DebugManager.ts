import { EventEmitter } from 'events';
import { exec } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { promisify } from 'util';
import { SemanticSearchEngine } from '../semantic/SemanticSearchEngine.js';
import { logger } from '../utils/logger.js';

const execAsync = promisify(exec);

/**
 * Debug event types
 */
export enum DebugEventType {
  ERROR_DETECTED = 'error_detected',
  FIX_SUGGESTED = 'fix_suggested',
  FIX_APPLIED = 'fix_applied',
  TEST_RUN = 'test_run',
}

/**
 * Debug event interface
 */
export interface DebugEvent {
  type: DebugEventType;
  message: string;
  details?: any;
  timestamp: number;
}

/**
 * Error location interface
 */
export interface ErrorLocation {
  filePath: string;
  line?: number;
  column?: number;
  message: string;
  stack?: string;
}

/**
 * Fix suggestion interface
 */
export interface FixSuggestion {
  errorLocation: ErrorLocation;
  suggestion: string;
  code?: string;
}

/**
 * DebugManager class for autonomous debugging
 */
export class DebugManager extends EventEmitter {
  private semanticSearch: SemanticSearchEngine;
  private activeDebuggingSession: boolean = false;
  private detectedErrors: ErrorLocation[] = [];
  private appliedFixes: Map<string, string> = new Map(); // key: filePath, value: original content
  
  constructor(semanticSearch: SemanticSearchEngine) {
    super();
    this.semanticSearch = semanticSearch;
  }
  
  /**
   * Start a debugging session
   */
  public async startDebuggingSession(): Promise<void> {
    if (this.activeDebuggingSession) {
      logger.warn('Debugging session already active');
      return;
    }
    
    logger.info('Starting debugging session');
    this.activeDebuggingSession = true;
    this.detectedErrors = [];
    this.appliedFixes.clear();
  }
  
  /**
   * End debugging session
   */
  public endDebuggingSession(): void {
    if (!this.activeDebuggingSession) {
      logger.warn('No active debugging session');
      return;
    }
    
    logger.info('Ending debugging session');
    this.activeDebuggingSession = false;
  }
  
  /**
   * Run a command and capture its output for debugging
   */
  public async runCommand(command: string): Promise<{ stdout: string; stderr: string; success: boolean }> {
    try {
      logger.info(`Running command for debugging: ${command}`);
      
      const { stdout, stderr } = await execAsync(command);
      
      // Check for errors in stderr
      if (stderr && stderr.length > 0) {
        // Parse errors from stderr
        const errors = this.parseErrorsFromOutput(stderr);
        
        if (errors.length > 0) {
          this.detectedErrors.push(...errors);
          
          // Emit error detected events
          for (const error of errors) {
            this.emitDebugEvent(DebugEventType.ERROR_DETECTED, `Error detected in ${error.filePath}${error.line ? ` at line ${error.line}` : ''}`, error);
          }
          
          return { stdout, stderr, success: false };
        }
      }
      
      return { stdout, stderr, success: true };
    } catch (error) {
      const errorOutput = error instanceof Error ? error.message : String(error);
      
      // Parse errors from error output
      const errors = this.parseErrorsFromOutput(errorOutput);
      
      if (errors.length > 0) {
        this.detectedErrors.push(...errors);
        
        // Emit error detected events
        for (const error of errors) {
          this.emitDebugEvent(DebugEventType.ERROR_DETECTED, `Error detected in ${error.filePath}${error.line ? ` at line ${error.line}` : ''}`, error);
        }
      }
      
      return { stdout: '', stderr: errorOutput, success: false };
    }
  }
  
  /**
   * Parse errors from command output
   */
  private parseErrorsFromOutput(output: string): ErrorLocation[] {
    const errors: ErrorLocation[] = [];
    
    // This is a simple parser that looks for common error patterns
    // In a production implementation, this would be much more sophisticated
    
    // TypeScript/JavaScript error pattern: file.ts(line,column): error message
    const tsErrorRegex = /([^(]+)\((\d+),(\d+)\):\s+(.+)/g;
    let match: RegExpExecArray | null;
    
    while ((match = tsErrorRegex.exec(output)) !== null) {
      errors.push({
        filePath: match[1],
        line: parseInt(match[2], 10),
        column: parseInt(match[3], 10),
        message: match[4],
      });
    }
    
    // Node.js runtime error pattern: at functionName (file.js:line:column)
    const nodeErrorRegex = /at\s+[\w.<>]+\s+\(?([^:]+):(\d+):(\d+)\)?/g;
    
    while ((match = nodeErrorRegex.exec(output)) !== null) {
      errors.push({
        filePath: match[1],
        line: parseInt(match[2], 10),
        column: parseInt(match[3], 10),
        message: 'Runtime error',
        stack: output,
      });
    }
    
    // If no specific errors were found but there's still an error output
    if (errors.length === 0 && output.trim().length > 0) {
      errors.push({
        filePath: 'unknown',
        message: output.trim(),
      });
    }
    
    return errors;
  }
  
  /**
   * Get detected errors from the current debugging session
   */
  public getDetectedErrors(): ErrorLocation[] {
    return [...this.detectedErrors];
  }
  
  /**
   * Generate fix suggestions for detected errors
   */
  public async generateFixSuggestions(): Promise<FixSuggestion[]> {
    if (this.detectedErrors.length === 0) {
      logger.info('No errors detected to generate fixes for');
      return [];
    }
    
    const suggestions: FixSuggestion[] = [];
    
    for (const error of this.detectedErrors) {
      // Skip errors with unknown file paths
      if (error.filePath === 'unknown') {
        continue;
      }
      
      try {
        // Try to read the file content
        const filePath = path.resolve(error.filePath);
        const fileContent = await fs.readFile(filePath, 'utf-8');
        
        // Get the relevant code snippet
        const lines = fileContent.split('\n');
        const startLine = Math.max(1, (error.line || 1) - 5);
        const endLine = Math.min(lines.length, (error.line || 1) + 5);
        const codeSnippet = lines.slice(startLine - 1, endLine).join('\n');
        
        // Use semantic search to find related code patterns
        if (error.line && error.message) {
          const searchQuery = `Fix ${error.message} in ${path.basename(error.filePath)}`;
          const semanticResults = await this.semanticSearch.search(searchQuery, 3);
          
          // In a production implementation, this would use an LLM to generate fixes
          // based on the error, the code context, and similar patterns in the codebase
          
          // For now, create a simple fix suggestion
          const suggestion: FixSuggestion = {
            errorLocation: error,
            suggestion: `Consider fixing the error: ${error.message}`,
            code: codeSnippet,
          };
          
          suggestions.push(suggestion);
          
          // Emit fix suggested event
          this.emitDebugEvent(
            DebugEventType.FIX_SUGGESTED,
            `Fix suggested for error in ${error.filePath}${error.line ? ` at line ${error.line}` : ''}`,
            suggestion
          );
        }
      } catch (readError) {
        logger.error(`Error reading file for fix suggestion: ${error.filePath}`, readError);
      }
    }
    
    return suggestions;
  }
  
  /**
   * Apply a fix to a file
   */
  public async applyFix(filePath: string, line: number, originalCode: string, fixedCode: string): Promise<boolean> {
    try {
      const resolvedPath = path.resolve(filePath);
      
      // Read the current file content
      const currentContent = await fs.readFile(resolvedPath, 'utf-8');
      
      // Store original content if not already stored
      if (!this.appliedFixes.has(resolvedPath)) {
        this.appliedFixes.set(resolvedPath, currentContent);
      }
      
      // Split into lines
      const lines = currentContent.split('\n');
      
      // Replace the line(s)
      const originalLines = originalCode.split('\n');
      const fixedLines = fixedCode.split('\n');
      
      // Ensure we have enough lines to replace
      if (line + originalLines.length - 1 > lines.length) {
        throw new Error('Line range to replace exceeds file length');
      }
      
      // Replace the lines
      for (let i = 0; i < originalLines.length; i++) {
        if (lines[line + i - 1].trim() !== originalLines[i].trim()) {
          throw new Error('Original code does not match file content at the specified line');
        }
      }
      
      // Apply the fix
      for (let i = 0; i < fixedLines.length; i++) {
        if (i < originalLines.length) {
          lines[line + i - 1] = fixedLines[i];
        } else {
          // Insert additional lines if the fix has more lines than the original
          lines.splice(line + i - 1, 0, fixedLines[i]);
        }
      }
      
      // Write the updated content back to the file
      await fs.writeFile(resolvedPath, lines.join('\n'), 'utf-8');
      
      // Emit fix applied event
      this.emitDebugEvent(
        DebugEventType.FIX_APPLIED,
        `Fix applied to ${filePath} at line ${line}`,
        { filePath, line, originalCode, fixedCode }
      );
      
      return true;
    } catch (error) {
      logger.error(`Error applying fix to ${filePath}:`, error);
      return false;
    }
  }
  
  /**
   * Revert applied fixes
   */
  public async revertFixes(): Promise<void> {
    for (const [filePath, originalContent] of this.appliedFixes.entries()) {
      try {
        await fs.writeFile(filePath, originalContent, 'utf-8');
        logger.info(`Reverted changes to ${filePath}`);
      } catch (error) {
        logger.error(`Error reverting changes to ${filePath}:`, error);
      }
    }
    
    this.appliedFixes.clear();
  }
  
  /**
   * Run tests for verification
   */
  public async runTests(testCommand: string): Promise<boolean> {
    try {
      logger.info(`Running tests: ${testCommand}`);
      
      const { stdout, stderr, success } = await this.runCommand(testCommand);
      
      // Emit test run event
      this.emitDebugEvent(
        DebugEventType.TEST_RUN,
        `Test run completed with ${success ? 'success' : 'failure'}`,
        { stdout, stderr, success }
      );
      
      return success;
    } catch (error) {
      logger.error('Error running tests:', error);
      
      // Emit test run event with failure
      this.emitDebugEvent(
        DebugEventType.TEST_RUN,
        'Test run failed with error',
        { error: error instanceof Error ? error.message : String(error) }
      );
      
      return false;
    }
  }
  
  /**
   * Emit a debug event
   */
  private emitDebugEvent(type: DebugEventType, message: string, details?: any): void {
    const event: DebugEvent = {
      type,
      message,
      details,
      timestamp: Date.now(),
    };
    
    this.emit('debugEvent', event);
  }
} 