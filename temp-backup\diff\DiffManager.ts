import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';
import * as diff from 'diff';
import chalk from 'chalk';
import { logger } from '../utils/logger.js';

/**
 * Interface for a diff hunk
 */
export interface DiffHunk {
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
  lines: string[];
}

/**
 * Interface for a diff
 */
export interface FileDiff {
  filePath: string;
  oldContent: string;
  newContent: string;
  hunks: DiffHunk[];
  timestamp: number;
  summary: {
    additions: number;
    deletions: number;
    changes: number;
  };
}

/**
 * Interface for file change tracking
 */
export interface FileChange {
  filePath: string;
  originalContent: string | null;
  timestamp: number;
}

/**
 * Diff Manager class for tracking and visualizing file changes
 */
export class DiffManager extends EventEmitter {
  private fileChanges: Map<string, FileChange> = new Map();
  private diffs: FileDiff[] = [];
  private maxDiffHistory: number = 50;
  
  constructor() {
    super();
  }
  
  /**
   * Track a file for changes
   */
  public async trackFileChange(filePath: string): Promise<void> {
    try {
      // Normalize the file path
      const normalizedPath = path.resolve(filePath);
      
      // Check if the file exists
      let content: string | null = null;
      try {
        const fileContent = await fs.readFile(normalizedPath, 'utf8');
        content = fileContent;
      } catch (error) {
        // File doesn't exist or is not readable
        logger.debug(`File ${normalizedPath} not found or not readable for tracking`);
      }
      
      // Store original content for future diff
      this.fileChanges.set(normalizedPath, {
        filePath: normalizedPath,
        originalContent: content,
        timestamp: Date.now()
      });
      
      logger.debug(`Tracking changes for file: ${normalizedPath}`);
    } catch (error) {
      logger.error(`Error tracking file change for ${filePath}:`, error);
    }
  }
  
  /**
   * Generate a diff for a file
   */
  public async generateFileDiff(filePath: string): Promise<FileDiff | null> {
    try {
      // Normalize the file path
      const normalizedPath = path.resolve(filePath);
      
      // Get tracked change
      const trackedChange = this.fileChanges.get(normalizedPath);
      
      if (!trackedChange) {
        logger.warn(`File ${normalizedPath} is not being tracked for changes`);
        return null;
      }
      
      // Get current content
      let currentContent: string | null = null;
      try {
        const fileContent = await fs.readFile(normalizedPath, 'utf8');
        currentContent = fileContent;
      } catch (error) {
        // File doesn't exist anymore or is not readable
        logger.debug(`File ${normalizedPath} not found or not readable for diff`);
        currentContent = '';
      }
      
      const originalContent = trackedChange.originalContent || '';
      
      // Generate the diff
      return this.generateDiff(originalContent, currentContent || '', normalizedPath);
    } catch (error) {
      logger.error(`Error generating diff for ${filePath}:`, error);
      return null;
    }
  }
  
  /**
   * Generate a diff between two text contents
   */
  public generateDiff(oldContent: string, newContent: string, filePath: string): FileDiff {
    // Normalize line endings
    const normalizedOldContent = oldContent.replace(/\r\n/g, '\n');
    const normalizedNewContent = newContent.replace(/\r\n/g, '\n');
    
    // Generate unified diff
    const patches = diff.structuredPatch(
      filePath,
      filePath,
      normalizedOldContent,
      normalizedNewContent,
      '',
      ''
    );
    
    // Count additions and deletions
    let additions = 0;
    let deletions = 0;
    let changes = 0;
    
    for (const hunk of patches.hunks) {
      for (const line of hunk.lines) {
        if (line.startsWith('+')) {
          additions++;
        } else if (line.startsWith('-')) {
          deletions++;
        } else if (line.startsWith(' ')) {
          changes++;
        }
      }
    }
    
    // Create the diff object
    const fileDiff: FileDiff = {
      filePath,
      oldContent: normalizedOldContent,
      newContent: normalizedNewContent,
      hunks: patches.hunks,
      timestamp: Date.now(),
      summary: {
        additions,
        deletions,
        changes
      }
    };
    
    // Store the diff
    this.diffs.push(fileDiff);
    
    // Trim history if needed
    if (this.diffs.length > this.maxDiffHistory) {
      this.diffs = this.diffs.slice(-this.maxDiffHistory);
    }
    
    // Emit event
    this.emit('diffGenerated', fileDiff);
    
    return fileDiff;
  }
  
  /**
   * Format a diff for terminal display
   */
  public formatDiffForTerminal(fileDiff: FileDiff): string {
    let output = '';
    
    // Add file header
    output += chalk.bold(`--- ${fileDiff.filePath}\n`);
    output += chalk.bold(`+++ ${fileDiff.filePath}\n`);
    
    // Add summary
    output += chalk.cyan(
      `@@ ${fileDiff.summary.additions} additions, ${fileDiff.summary.deletions} deletions @@\n`
    );
    
    // Add hunks
    for (const hunk of fileDiff.hunks) {
      output += chalk.cyan(
        `@@ -${hunk.oldStart},${hunk.oldLines} +${hunk.newStart},${hunk.newLines} @@\n`
      );
      
      for (const line of hunk.lines) {
        if (line.startsWith('+')) {
          output += chalk.green(line) + '\n';
        } else if (line.startsWith('-')) {
          output += chalk.red(line) + '\n';
        } else {
          output += line + '\n';
        }
      }
    }
    
    return output;
  }
  
  /**
   * Get a specific diff by file path
   */
  public getDiff(filePath: string): FileDiff | undefined {
    const normalizedPath = path.resolve(filePath);
    return this.diffs.find(diff => diff.filePath === normalizedPath);
  }
  
  /**
   * Get all tracked diffs
   */
  public getAllDiffs(): FileDiff[] {
    return [...this.diffs];
  }
  
  /**
   * Get all tracked file changes
   */
  public getTrackedFiles(): string[] {
    return Array.from(this.fileChanges.keys());
  }
  
  /**
   * Untrack a file
   */
  public untrackFile(filePath: string): boolean {
    const normalizedPath = path.resolve(filePath);
    return this.fileChanges.delete(normalizedPath);
  }
  
  /**
   * Reset tracking for all files
   */
  public resetTracking(): void {
    this.fileChanges.clear();
    logger.debug('Diff tracking reset for all files');
  }
  
  /**
   * Set maximum diff history
   */
  public setMaxDiffHistory(max: number): void {
    this.maxDiffHistory = max;
    
    // Trim history if needed
    if (this.diffs.length > this.maxDiffHistory) {
      this.diffs = this.diffs.slice(-this.maxDiffHistory);
    }
  }
  
  /**
   * Compares two files and generates a diff
   */
  public async compareFiles(filePath1: string, filePath2: string): Promise<FileDiff | null> {
    try {
      // Read the two files
      const content1 = await fs.readFile(filePath1, 'utf8');
      const content2 = await fs.readFile(filePath2, 'utf8');
      
      // Generate the diff
      return this.generateDiff(content1, content2, `${filePath1} ↔ ${filePath2}`);
    } catch (error) {
      logger.error(`Error comparing files ${filePath1} and ${filePath2}:`, error);
      return null;
    }
  }
  
  /**
   * Apply a patch to a string
   */
  public applyPatch(content: string, fileDiff: FileDiff): string {
    const patches = diff.structuredPatch(
      fileDiff.filePath,
      fileDiff.filePath,
      fileDiff.oldContent,
      fileDiff.newContent,
      '',
      ''
    );
    
    return diff.applyPatch(content, patches);
  }
  
  /**
   * Apply a diff to a file
   */
  public async applyDiffToFile(filePath: string, fileDiff: FileDiff): Promise<boolean> {
    try {
      // Read the current content
      const content = await fs.readFile(filePath, 'utf8');
      
      // Apply the patch
      const patchedContent = this.applyPatch(content, fileDiff);
      
      // Write the patched content
      await fs.writeFile(filePath, patchedContent, 'utf8');
      
      return true;
    } catch (error) {
      logger.error(`Error applying diff to file ${filePath}:`, error);
      return false;
    }
  }
} 