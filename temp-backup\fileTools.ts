import { ToolRegistry } from './ToolRegistry.js';
import { z } from 'zod';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';
import { glob } from 'fast-glob';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import { DiffManager } from '../diff/DiffManager.js';

/**
 * Register all file-related tools
 */
export function registerFileTools(registry: ToolRegistry): void {
  logger.info('Registering file tools');
  
  // Read File Tool
  registry.registerTool({
    name: 'file_read',
    description: 'Read the contents of a file',
    parameters: z.object({
      path: z.string().describe('Path to the file to read')
    }),
    execute: async (params: { path: string }) => {
      try {
        const content = await fs.readFile(params.path, 'utf-8');
        
        return {
          success: true,
          content,
          path: params.path
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          path: params.path
        };
      }
    }
  });
  
  // Write File Tool
  registry.registerTool({
    name: 'file_write',
    description: 'Write content to a file',
    parameters: z.object({
      path: z.string().describe('Path to the file to write'),
      content: z.string().describe('Content to write to the file'),
      append: z.boolean().optional().describe('Whether to append to the file instead of overwriting (default: false)')
    }),
    execute: async (params: { path: string; content: string; append?: boolean }) => {
      try {
        const flag = params.append ? 'a' : 'w';
        
        // Create directories if they don't exist
        const dir = path.dirname(params.path);
        await fs.mkdir(dir, { recursive: true });
        
        await fs.writeFile(params.path, params.content, { flag });
        
        return {
          success: true,
          path: params.path,
          operation: params.append ? 'append' : 'write'
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          path: params.path
        };
      }
    }
  });
  
  // List Directory Tool
  registry.registerTool({
    name: 'file_list',
    description: 'List files in a directory',
    parameters: z.object({
      path: z.string().describe('Path to the directory to list'),
      recursive: z.boolean().optional().describe('Whether to list files recursively (default: false)')
    }),
    execute: async (params: { path: string; recursive?: boolean }) => {
      try {
        const files = await fs.readdir(params.path, { withFileTypes: true });
        
        const result: { name: string; isDirectory: boolean; path: string }[] = [];
        
        for (const file of files) {
          const filePath = path.join(params.path, file.name);
          
          result.push({
            name: file.name,
            isDirectory: file.isDirectory(),
            path: filePath
          });
          
          // If recursive and it's a directory, list its contents too
          if (params.recursive && file.isDirectory()) {
            const subResult = await registry.executeTool('file_list', {
              path: filePath,
              recursive: true
            });
            
            if (subResult.success && subResult.files) {
              result.push(...subResult.files);
            }
          }
        }
        
        return {
          success: true,
          files: result,
          path: params.path
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          path: params.path
        };
      }
    }
  });
  
  // Delete File Tool
  registry.registerTool({
    name: 'file_delete',
    description: 'Delete a file or directory',
    parameters: z.object({
      path: z.string().describe('Path to the file or directory to delete'),
      recursive: z.boolean().optional().describe('Whether to recursively delete directories (default: false)')
    }),
    execute: async (params: { path: string; recursive?: boolean }) => {
      try {
        const stats = await fs.stat(params.path);
        
        if (stats.isDirectory()) {
          if (params.recursive) {
            await fs.rm(params.path, { recursive: true });
          } else {
            await fs.rmdir(params.path);
          }
        } else {
          await fs.unlink(params.path);
        }
        
        return {
          success: true,
          path: params.path,
          type: stats.isDirectory() ? 'directory' : 'file'
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          path: params.path
        };
      }
    }
  });
  
  // Create Directory Tool
  registry.registerTool({
    name: 'file_mkdir',
    description: 'Create a directory',
    parameters: z.object({
      path: z.string().describe('Path to the directory to create'),
      recursive: z.boolean().optional().describe('Whether to create parent directories if they do not exist (default: true)')
    }),
    execute: async (params: { path: string; recursive?: boolean }) => {
      try {
        await fs.mkdir(params.path, { recursive: params.recursive !== false });
        
        return {
          success: true,
          path: params.path
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          path: params.path
        };
      }
    }
  });
  
  // Rename File Tool
  registry.registerTool({
    name: 'file_rename',
    description: 'Rename or move a file or directory',
    parameters: z.object({
      source: z.string().describe('Path to the file or directory to rename'),
      destination: z.string().describe('New path for the file or directory')
    }),
    execute: async (params: { source: string; destination: string }) => {
      try {
        // Create parent directories if they don't exist
        const dir = path.dirname(params.destination);
        await fs.mkdir(dir, { recursive: true });
        
        await fs.rename(params.source, params.destination);
        
        return {
          success: true,
          source: params.source,
          destination: params.destination
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          source: params.source,
          destination: params.destination
        };
      }
    }
  });
  
  // Copy File Tool
  registry.registerTool({
    name: 'file_copy',
    description: 'Copy a file or directory',
    parameters: z.object({
      source: z.string().describe('Path to the file or directory to copy'),
      destination: z.string().describe('Destination path'),
      recursive: z.boolean().optional().describe('Whether to recursively copy directories (default: true)')
    }),
    execute: async (params: { source: string; destination: string; recursive?: boolean }) => {
      try {
        const stats = await fs.stat(params.source);
        
        // Create parent directories if they don't exist
        const dir = path.dirname(params.destination);
        await fs.mkdir(dir, { recursive: true });
        
        if (stats.isDirectory()) {
          if (params.recursive !== false) {
            // For directories, we need to implement recursive copy
            // Create the destination directory
            await fs.mkdir(params.destination, { recursive: true });
            
            // Read source directory contents
            const files = await fs.readdir(params.source, { withFileTypes: true });
            
            // Copy each file/subdirectory
            for (const file of files) {
              const sourcePath = path.join(params.source, file.name);
              const destPath = path.join(params.destination, file.name);
              
              if (file.isDirectory()) {
                await registry.executeTool('file_copy', {
                  source: sourcePath,
                  destination: destPath,
                  recursive: true
                });
              } else {
                await fs.copyFile(sourcePath, destPath);
              }
            }
          } else {
            // Just create an empty directory if recursive is false
            await fs.mkdir(params.destination, { recursive: true });
          }
        } else {
          // Simple file copy
          await fs.copyFile(params.source, params.destination);
        }
        
        return {
          success: true,
          source: params.source,
          destination: params.destination,
          type: stats.isDirectory() ? 'directory' : 'file'
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          source: params.source,
          destination: params.destination
        };
      }
    }
  });
  
  // Check if File Exists Tool
  registry.registerTool({
    name: 'file_exists',
    description: 'Check if a file or directory exists',
    parameters: z.object({
      path: z.string().describe('Path to check')
    }),
    execute: async (params: { path: string }) => {
      try {
        const stats = await fs.stat(params.path);
        
        return {
          success: true,
          exists: true,
          isDirectory: stats.isDirectory(),
          isFile: stats.isFile(),
          path: params.path
        };
      } catch (error) {
        if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
          return {
            success: true,
            exists: false,
            path: params.path
          };
        }
        
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          path: params.path
        };
      }
    }
  });
  
  // Find Files Tool
  registry.registerTool({
    name: 'file_find',
    description: 'Find files matching a pattern',
    parameters: z.object({
      patterns: z.array(z.string()).describe('Glob patterns to match'),
      ignore: z.array(z.string()).optional().describe('Patterns to ignore'),
      cwd: z.string().optional().describe('Working directory for the search (default: current directory)')
    }),
    execute: async (params: { patterns: string[]; ignore?: string[]; cwd?: string }) => {
      try {
        const cwd = params.cwd || process.cwd();
        
        const files = await glob(params.patterns, {
          ignore: params.ignore,
          cwd,
          absolute: true
        });
        
        return {
          success: true,
          files,
          count: files.length,
          patterns: params.patterns,
          cwd
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          patterns: params.patterns
        };
      }
    }
  });
  
  // Get File Info Tool
  registry.registerTool({
    name: 'file_info',
    description: 'Get information about a file or directory',
    parameters: z.object({
      path: z.string().describe('Path to get information about')
    }),
    execute: async (params: { path: string }) => {
      try {
        const stats = await fs.stat(params.path);
        
        return {
          success: true,
          path: params.path,
          size: stats.size,
          isDirectory: stats.isDirectory(),
          isFile: stats.isFile(),
          isSymbolicLink: stats.isSymbolicLink(),
          created: stats.birthtime,
          modified: stats.mtime,
          accessed: stats.atime
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          path: params.path
        };
      }
    }
  });
  
  logger.info('File tools registered successfully');
} 