import { EventEmitter } from 'events';
import simplegit, { SimpleGit } from 'simple-git';
import path from 'path';
import fs from 'fs/promises';
import { logger } from '../utils/logger.js';
import { DiffManager, FileDiff } from '../diff/DiffManager.js';
import { isomorphicPath } from '../utils/isomorphicPath.js';

/**
 * Git operation types
 */
export enum GitOperationType {
  COMMIT = 'commit',
  BRANCH = 'branch',
  CHECKOUT = 'checkout',
  MERGE = 'merge',
  PULL = 'pull',
  PUSH = 'push',
  STATUS = 'status',
  LOG = 'log',
  DIFF = 'diff',
}

/**
 * Git operation result interface
 */
export interface GitOperationResult {
  type: GitOperationType;
  success: boolean;
  message: string;
  details?: any;
  timestamp: number;
}

/**
 * Repository info interface
 */
export interface RepositoryInfo {
  path: string;
  currentBranch: string;
  isClean: boolean;
  remotes: string[];
  branches: {
    current: string;
    local: string[];
    remote: string[];
  };
  lastCommit?: {
    hash: string;
    message: string;
    author: string;
    date: Date;
  };
}

/**
 * Commit info interface
 */
export interface CommitInfo {
  hash: string;
  date: Date;
  message: string;
  author_name: string;
  author_email: string;
}

/**
 * GitManager class for managing git operations
 */
export class GitManager extends EventEmitter {
  private git: SimpleGit | null = null;
  private isGitRepo: boolean = false;
  private repoPath: string = '';
  private diffManager: DiffManager;
  private repositoryInfo: RepositoryInfo | null = null;
  
  constructor(diffManager: DiffManager) {
    super();
    this.diffManager = diffManager;
  }
  
  /**
   * Initialize git functionality
   */
  public async initialize(): Promise<boolean> {
    try {
      // Try to find git repository
      this.repoPath = process.cwd();
      this.git = simplegit(this.repoPath);
      
      // Check if current directory is a git repository
      this.isGitRepo = await this.git.checkIsRepo();
      
      if (this.isGitRepo) {
        logger.info(`Git repository found at ${this.repoPath}`);
        await this.updateRepositoryState();
        return true;
      } else {
        logger.info('No git repository found in current directory');
        return false;
      }
    } catch (error) {
      logger.error('Error initializing git manager:', error);
      this.isGitRepo = false;
      return false;
    }
  }
  
  /**
   * Update repository state information
   */
  public async updateRepositoryState(): Promise<RepositoryInfo | null> {
    if (!this.isGitRepo || !this.git) {
      return null;
    }
    
    try {
      // Get current branch
      const currentBranch = await this.git.branch();
      
      // Get status
      const status = await this.git.status();
      
      // Get remotes
      const remotes = await this.git.getRemotes();
      
      // Get last commit
      const log = await this.git.log({ maxCount: 1 });
      
      this.repositoryInfo = {
        path: this.repoPath,
        currentBranch: currentBranch.current,
        isClean: status.isClean(),
        remotes: remotes.map(remote => remote.name),
        branches: {
          current: currentBranch.current,
          local: currentBranch.all.filter(b => !b.startsWith('remotes/')),
          remote: currentBranch.all.filter(b => b.startsWith('remotes/')),
        },
      };
      
      // Add last commit info if available
      if (log.latest) {
        this.repositoryInfo.lastCommit = {
          hash: log.latest.hash,
          message: log.latest.message,
          author: `${log.latest.author_name} <${log.latest.author_email}>`,
          date: new Date(log.latest.date),
        };
      }
      
      return this.repositoryInfo;
    } catch (error) {
      logger.error('Error updating repository state:', error);
      return null;
    }
  }
  
  /**
   * Get repository information
   */
  public getRepositoryInfo(): RepositoryInfo | null {
    return this.repositoryInfo;
  }
  
  /**
   * Check if current directory is a git repository
   */
  public isGitRepository(): boolean {
    return this.isGitRepo;
  }
  
  /**
   * Create a new branch
   */
  public async createBranch(branchName: string): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.BRANCH, 'Not a git repository');
    }
    
    try {
      await this.git.checkoutLocalBranch(branchName);
      
      const result: GitOperationResult = {
        type: GitOperationType.BRANCH,
        success: true,
        message: `Created and switched to branch '${branchName}'`,
        timestamp: Date.now(),
      };
      
      await this.updateRepositoryState();
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.BRANCH, 
        `Failed to create branch '${branchName}': ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Switch to a branch
   */
  public async checkoutBranch(branchName: string): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.CHECKOUT, 'Not a git repository');
    }
    
    try {
      await this.git.checkout(branchName);
      
      const result: GitOperationResult = {
        type: GitOperationType.CHECKOUT,
        success: true,
        message: `Switched to branch '${branchName}'`,
        timestamp: Date.now(),
      };
      
      await this.updateRepositoryState();
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.CHECKOUT,
        `Failed to checkout branch '${branchName}': ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Commit changes
   */
  public async commitChanges(message: string, files?: string[]): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.COMMIT, 'Not a git repository');
    }
    
    try {
      // Add files to staging area
      if (files && files.length > 0) {
        await this.git.add(files);
      } else {
        await this.git.add('.');
      }
      
      // Commit the changes
      const commitResult = await this.git.commit(message);
      
      const result: GitOperationResult = {
        type: GitOperationType.COMMIT,
        success: true,
        message: `Committed changes: ${message}`,
        details: commitResult,
        timestamp: Date.now(),
      };
      
      await this.updateRepositoryState();
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.COMMIT,
        `Failed to commit changes: ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Get repository status
   */
  public async getStatus(): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.STATUS, 'Not a git repository');
    }
    
    try {
      const status = await this.git.status();
      
      const result: GitOperationResult = {
        type: GitOperationType.STATUS,
        success: true,
        message: 'Retrieved repository status',
        details: status,
        timestamp: Date.now(),
      };
      
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.STATUS,
        `Failed to get status: ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Get commit history
   */
  public async getCommitHistory(maxCount: number = 10): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.LOG, 'Not a git repository');
    }
    
    try {
      const log = await this.git.log({ maxCount });
      
      const result: GitOperationResult = {
        type: GitOperationType.LOG,
        success: true,
        message: `Retrieved ${log.total} commit(s)`,
        details: log,
        timestamp: Date.now(),
      };
      
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.LOG,
        `Failed to get commit history: ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Pull changes from remote
   */
  public async pullChanges(remote: string = 'origin', branch?: string): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.PULL, 'Not a git repository');
    }
    
    try {
      let pullOptions = {};
      
      if (branch) {
        pullOptions = { remote, branch };
      }
      
      const pullResult = await this.git.pull(pullOptions);
      
      const result: GitOperationResult = {
        type: GitOperationType.PULL,
        success: true,
        message: `Pulled changes from ${remote}${branch ? `/${branch}` : ''}`,
        details: pullResult,
        timestamp: Date.now(),
      };
      
      await this.updateRepositoryState();
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.PULL,
        `Failed to pull changes: ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Push changes to remote
   */
  public async pushChanges(remote: string = 'origin', branch?: string): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.PUSH, 'Not a git repository');
    }
    
    try {
      let pushOptions = [];
      
      if (branch) {
        pushOptions = [remote, branch];
      }
      
      const pushResult = await this.git.push(pushOptions);
      
      const result: GitOperationResult = {
        type: GitOperationType.PUSH,
        success: true,
        message: `Pushed changes to ${remote}${branch ? `/${branch}` : ''}`,
        details: pushResult,
        timestamp: Date.now(),
      };
      
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.PUSH,
        `Failed to push changes: ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Get diff between commits or files
   */
  public async getDiff(commitHash1?: string, commitHash2?: string): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.DIFF, 'Not a git repository');
    }
    
    try {
      let diffResult;
      
      if (commitHash1 && commitHash2) {
        diffResult = await this.git.diff([commitHash1, commitHash2]);
      } else if (commitHash1) {
        diffResult = await this.git.diff([commitHash1]);
      } else {
        // Get diff of working directory changes
        diffResult = await this.git.diff();
      }
      
      const result: GitOperationResult = {
        type: GitOperationType.DIFF,
        success: true,
        message: 'Retrieved diff',
        details: diffResult,
        timestamp: Date.now(),
      };
      
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.DIFF,
        `Failed to get diff: ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Create an error result
   */
  private errorResult(type: GitOperationType, message: string): GitOperationResult {
    return {
      type,
      success: false,
      message,
      timestamp: Date.now(),
    };
  }
  
  /**
   * Merge a branch into current branch
   */
  public async mergeBranch(branchName: string): Promise<GitOperationResult> {
    if (!this.isGitRepo || !this.git) {
      return this.errorResult(GitOperationType.MERGE, 'Not a git repository');
    }
    
    try {
      const mergeResult = await this.git.merge([branchName]);
      
      const result: GitOperationResult = {
        type: GitOperationType.MERGE,
        success: true,
        message: `Merged branch '${branchName}' into current branch`,
        details: mergeResult,
        timestamp: Date.now(),
      };
      
      await this.updateRepositoryState();
      this.emit('gitOperation', result);
      
      return result;
    } catch (error) {
      const result = this.errorResult(
        GitOperationType.MERGE,
        `Failed to merge branch '${branchName}': ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('gitOperation', result);
      return result;
    }
  }
  
  /**
   * Get visual representation of git history
   */
  public async getVisualHistory(maxCount: number = 20): Promise<string> {
    if (!this.isGitRepo || !this.git) {
      return 'Not a git repository';
    }
    
    try {
      const log = await this.git.raw(['log', '--graph', '--oneline', '--decorate', `-n${maxCount}`]);
      return log;
    } catch (error) {
      logger.error('Error getting visual history:', error);
      return `Error getting visual history: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
} 