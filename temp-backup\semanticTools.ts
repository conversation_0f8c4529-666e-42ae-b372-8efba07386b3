import { z } from 'zod';
import { ToolRegistry } from './ToolRegistry.js';
import { getSemanticSearchEngine } from '../semantic/semanticSetup.js';
import { logger } from '../utils/logger.js';

/**
 * Register all semantic search tools
 */
export function registerSemanticTools(registry: ToolRegistry): void {
  logger.info('Registering semantic tools');
  
  // Semantic Search Tool
  registry.registerTool({
    name: 'semantic_search',
    description: 'Search codebase using semantic understanding',
    parameters: z.object({
      query: z.string().describe('The search query'),
      max_results: z.number().optional().describe('Maximum number of results to return (default: 5)')
    }),
    execute: async (params: { query: string; max_results?: number }) => {
      const semanticSearch = getSemanticSearchEngine();
      const results = await semanticSearch.search(params.query, params.max_results || 5);
      return results;
    }
  });
  
  // Semantic Code Indexing Tool
  registry.registerTool({
    name: 'semantic_index',
    description: 'Index codebase for semantic search',
    parameters: z.object({}),
    execute: async () => {
      const semanticSearch = getSemanticSearchEngine();
      await semanticSearch.indexProject();
      return { success: true, message: 'Codebase indexed successfully' };
    }
  });
  
  // Semantic File Update Tool
  registry.registerTool({
    name: 'semantic_update_file',
    description: 'Update semantic embeddings for a specific file',
    parameters: z.object({
      file_path: z.string().describe('Path to the file to update')
    }),
    execute: async (params: { file_path: string }) => {
      const semanticSearch = getSemanticSearchEngine();
      await semanticSearch.updateFileEmbeddings(params.file_path);
      return { success: true, message: `Embeddings updated for ${params.file_path}` };
    }
  });
  
  logger.info('Semantic tools registered successfully');
} 