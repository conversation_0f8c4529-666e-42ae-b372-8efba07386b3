import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';
import { glob } from 'fast-glob';
import { ContextManager } from '../context/ContextManager.js';
import { logger } from '../utils/logger.js';
import { Config } from '../utils/config.js';

/**
 * Interface for embedding vector
 */
export interface EmbeddingVector {
  vector: number[];
  filePath: string;
  content: string;
  startLine: number;
  endLine: number;
}

/**
 * Interface for semantic search match
 */
export interface SemanticSearchMatch {
  filePath: string;
  score: number;
  content: string;
  startLine: number;
  endLine: number;
}

/**
 * Interface for semantic search results
 */
export interface SemanticSearchResults {
  query: string;
  matches: SemanticSearchMatch[];
  timestamp: number;
}

/**
 * Semantic Search Engine for code understanding and search
 */
export class SemanticSearchEngine extends EventEmitter {
  private embeddings: EmbeddingVector[] = [];
  private contextManager: ContextManager;
  private config: Config | null = null;
  private embeddingModel: string = 'text-embedding-ada-002';
  private embeddingProvider: string = 'openai';
  
  constructor(contextManager: ContextManager) {
    super();
    this.contextManager = contextManager;
  }
  
  /**
   * Initialize the semantic search engine
   */
  public async initialize(config?: Config): Promise<void> {
    try {
      logger.info('Initializing semantic search engine');
      
      if (config) {
        this.config = config;
        
        // Set embedding model and provider from config if available
        if (config.aiSettings?.embeddingModel) {
          this.embeddingModel = config.aiSettings.embeddingModel;
        }
        
        if (config.aiSettings?.embeddingProvider) {
          this.embeddingProvider = config.aiSettings.embeddingProvider;
        }
      }
      
      // Load existing embeddings if available
      await this.loadEmbeddings();
      
      logger.info('Semantic search engine initialized');
    } catch (error) {
      logger.error('Error initializing semantic search engine:', error);
      throw error;
    }
  }
  
  /**
   * Perform semantic search on code
   */
  public async search(query: string, maxResults: number = 5): Promise<SemanticSearchResults> {
    try {
      logger.info(`Performing semantic search for: ${query}`);
      
      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);
      
      // If embeddings aren't generated yet, index the project first
      if (this.embeddings.length === 0) {
        await this.indexProject();
      }
      
      // Calculate cosine similarity between query and all embeddings
      const matches = this.embeddings
        .map(embedding => {
          const score = this.cosineSimilarity(queryEmbedding, embedding.vector);
          return {
            filePath: embedding.filePath,
            score,
            content: embedding.content,
            startLine: embedding.startLine,
            endLine: embedding.endLine,
          };
        })
        .sort((a, b) => b.score - a.score)
        .slice(0, maxResults);
      
      const results: SemanticSearchResults = {
        query,
        matches,
        timestamp: Date.now(),
      };
      
      // Emit search results
      this.emit('searchResults', results);
      
      return results;
    } catch (error) {
      logger.error('Error performing semantic search:', error);
      throw error;
    }
  }
  
  /**
   * Generate embedding for text
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      // In a production implementation, this would call OpenAI's embedding API
      // For now, we'll use a simple mock implementation
      
      // TODO: Replace with actual embedding generation using OpenAI SDK
      // This is a placeholder that returns a random embedding vector
      const dimension = 1536; // Same as OpenAI's ada-002 model
      const embedding = Array.from({ length: dimension }, () => Math.random() * 2 - 1);
      
      // Normalize the vector
      const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
      return embedding.map(val => val / magnitude);
      
      // In production:
      /*
      const openai = new OpenAI({ apiKey: this.config?.openAIKey });
      const response = await openai.embeddings.create({
        model: this.embeddingModel,
        input: text
      });
      return response.data[0].embedding;
      */
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }
  
  /**
   * Index the project for semantic search
   */
  public async indexProject(): Promise<void> {
    try {
      logger.info('Indexing project for semantic search');
      
      // Clear existing embeddings
      this.embeddings = [];
      
      // Get all source files
      const sourceFiles = await glob(['**/*.ts', '**/*.js', '**/*.tsx', '**/*.jsx'], {
        ignore: ['**/node_modules/**', '**/dist/**', '**/build/**'],
        cwd: process.cwd(),
      });
      
      logger.info(`Found ${sourceFiles.length} source files to index`);
      
      // Process each file
      for (const filePath of sourceFiles) {
        await this.indexFile(filePath);
      }
      
      logger.info(`Created ${this.embeddings.length} code embeddings`);
      
      // Save embeddings
      await this.saveEmbeddings();
    } catch (error) {
      logger.error('Error indexing project:', error);
      throw error;
    }
  }
  
  /**
   * Index a single file
   */
  private async indexFile(filePath: string): Promise<void> {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const content = await fs.readFile(fullPath, 'utf-8');
      
      // Split content into chunks (functions, classes, etc.)
      const chunks = this.splitIntoChunks(content);
      
      // Generate embeddings for each chunk
      for (const chunk of chunks) {
        const embedding = await this.generateEmbedding(chunk.content);
        
        this.embeddings.push({
          vector: embedding,
          filePath,
          content: chunk.content,
          startLine: chunk.startLine,
          endLine: chunk.endLine,
        });
      }
    } catch (error) {
      logger.error(`Error indexing file ${filePath}:`, error);
    }
  }
  
  /**
   * Split file content into semantic chunks
   */
  private splitIntoChunks(content: string): { content: string; startLine: number; endLine: number }[] {
    const lines = content.split('\n');
    const chunks: { content: string; startLine: number; endLine: number }[] = [];
    
    // Simple chunking strategy: Split by function/class definitions
    // In a more advanced implementation, this would use an AST parser
    
    let currentChunk: string[] = [];
    let startLine = 1;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      currentChunk.push(line);
      
      // If line contains a function/class definition or is end of file
      if (
        (line.match(/function\s+\w+\s*\(/) || 
        line.match(/class\s+\w+/) || 
        line.match(/const\s+\w+\s*=\s*\(/) ||
        line.match(/export\s+/) ||
        i === lines.length - 1) && 
        currentChunk.length > 0
      ) {
        // Add current chunk
        chunks.push({
          content: currentChunk.join('\n'),
          startLine,
          endLine: startLine + currentChunk.length - 1,
        });
        
        // Reset for next chunk
        currentChunk = [];
        startLine = i + 2;
      }
    }
    
    // If there's any remaining content
    if (currentChunk.length > 0) {
      chunks.push({
        content: currentChunk.join('\n'),
        startLine,
        endLine: startLine + currentChunk.length - 1,
      });
    }
    
    return chunks;
  }
  
  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same dimensions');
    }
    
    let dotProduct = 0;
    let magnitudeA = 0;
    let magnitudeB = 0;
    
    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      magnitudeA += vectorA[i] * vectorA[i];
      magnitudeB += vectorB[i] * vectorB[i];
    }
    
    magnitudeA = Math.sqrt(magnitudeA);
    magnitudeB = Math.sqrt(magnitudeB);
    
    return dotProduct / (magnitudeA * magnitudeB);
  }
  
  /**
   * Save embeddings to disk
   */
  private async saveEmbeddings(): Promise<void> {
    try {
      const embeddingsDir = path.join(process.cwd(), '.agent', 'embeddings');
      
      // Create directories if they don't exist
      await fs.mkdir(embeddingsDir, { recursive: true });
      
      // Save embeddings
      const embeddingsFile = path.join(embeddingsDir, 'embeddings.json');
      await fs.writeFile(embeddingsFile, JSON.stringify(this.embeddings), 'utf-8');
      
      logger.info(`Saved ${this.embeddings.length} embeddings to ${embeddingsFile}`);
    } catch (error) {
      logger.error('Error saving embeddings:', error);
    }
  }
  
  /**
   * Load embeddings from disk
   */
  private async loadEmbeddings(): Promise<void> {
    try {
      const embeddingsFile = path.join(process.cwd(), '.agent', 'embeddings', 'embeddings.json');
      
      // Check if embeddings file exists
      try {
        await fs.access(embeddingsFile);
      } catch {
        logger.info('No existing embeddings found');
        return;
      }
      
      // Load embeddings
      const data = await fs.readFile(embeddingsFile, 'utf-8');
      this.embeddings = JSON.parse(data);
      
      logger.info(`Loaded ${this.embeddings.length} embeddings from disk`);
    } catch (error) {
      logger.error('Error loading embeddings:', error);
    }
  }
  
  /**
   * Update embeddings for a specific file
   */
  public async updateFileEmbeddings(filePath: string): Promise<void> {
    try {
      // Remove existing embeddings for the file
      this.embeddings = this.embeddings.filter(e => e.filePath !== filePath);
      
      // Index the file again
      await this.indexFile(filePath);
      
      // Save updated embeddings
      await this.saveEmbeddings();
    } catch (error) {
      logger.error(`Error updating embeddings for ${filePath}:`, error);
    }
  }
} 