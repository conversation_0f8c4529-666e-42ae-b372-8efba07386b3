{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "resolveJsonModule": true, "outDir": "dist", "strict": true, "declaration": true, "skipLibCheck": true, "sourceMap": true, "lib": ["ES2022", "DOM"], "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "src/agent/**/*", "src/ai/**/*", "src/context/**/*", "src/debug/**/*", "src/diff/**/*", "src/git/**/*", "src/semantic/**/*"]}